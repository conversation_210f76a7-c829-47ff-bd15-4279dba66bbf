import type { NextConfig } from "next";

import { config } from "dotenv";

config();

const nextConfig: NextConfig = {
  // Configure Next.js to handle static assets properly
  output: 'standalone', // Optimizes for production deployments

  // Configure font optimization
  optimizeFonts: true,
  env: {
    // To use environment variables in the project without using the "NEXT_PUBLIC_" prefix
    // BASE_URL: process.env.BASE_URL,
    // DASHBOARD_BASE_URL: process.env.DASHBOARD_BASE_URL,
    ASSETS_URL: process.env.ASSETS_URL,
    GA_KEY: process.env.GA_KEY
  },
  images: {
    domains: [
      'fedhacore-assets.s3.us-east-1.amazonaws.com',
      's3.us-east-1.amazonaws.com',
      'fedhacore-assets.s3.amazonaws.com'
    ],
    remotePatterns: [
      {
        protocol: "http",
        hostname: "localhost"
      },
      {
        protocol: "https",
        hostname: "fedhacore-assets.s3.us-east-1.amazonaws.com",
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '*.amazonaws.com',
        pathname: '/**',
      },
      
      {
        protocol: "https",
        hostname: "stage.fedha.link"
      },
      {
        protocol: "https",
        hostname: "fedha.link"
      },
      {
        protocol: "https",
        hostname: "pay.fedha.link"
      },
      {
        protocol: "https",
        hostname: "app.fedha.link"
      }
    ]
  }
};

export default nextConfig;
