'use client';
import { Heading, Text, Card, Box } from "@radix-ui/themes";

export default function UlimwenguPage() {
  return (
    <div style={{ padding: "20px" }}>
      <Heading as="h1" size="8" style={{ marginBottom: "20px" }}>
        Welcome to Mwalimu
      </Heading>

      <Text as="p" size="4" style={{ marginBottom: "30px", color: "var(--gray-11)" }}>
        This is the main content area. The sidebar should stay fixed on the left side.
      </Text>

      <div style={{ display: "grid", gap: "20px", gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))" }}>
        <Card style={{ padding: "20px" }}>
          <Heading as="h3" size="4" style={{ marginBottom: "10px" }}>
            Dashboard
          </Heading>
          <Text>
            Your main dashboard content goes here. This area should scroll independently
            while the sidebar remains fixed.
          </Text>
        </Card>

        <Card style={{ padding: "20px" }}>
          <Heading as="h3" size="4" style={{ marginBottom: "10px" }}>
            Recent Activity
          </Heading>
          <Text>
            Recent activity and updates will be displayed in this section.
          </Text>
        </Card>

        <Card style={{ padding: "20px" }}>
          <Heading as="h3" size="4" style={{ marginBottom: "10px" }}>
            Quick Actions
          </Heading>
          <Text>
            Quick action buttons and shortcuts for common tasks.
          </Text>
        </Card>
      </div>

      {/* Add some content to test scrolling */}
      <div style={{ marginTop: "40px" }}>
        <Heading as="h2" size="6" style={{ marginBottom: "20px" }}>
          Test Content for Scrolling
        </Heading>
        {Array.from({ length: 10 }, (_, i) => (
          <Box key={i} style={{ marginBottom: "20px", padding: "15px", border: "1px solid var(--gray-6)", borderRadius: "8px" }}>
            <Text>
              This is test content item {i + 1}. The sidebar should remain fixed while this content scrolls.
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </Text>
          </Box>
        ))}
      </div>
    </div>
  );
}
