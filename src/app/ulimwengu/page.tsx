'use client';
import { C<PERSON>B<PERSON>, LibraryBox } from "@/components";
import { Heading, Text } from "@radix-ui/themes";

export default function UlimwenguPage() {
  return (
    <div style={{ padding: "20px" }}>
      <div style={{
        marginBottom: "10px",
        borderRadius: "24px",
        padding: "32px 0px",
      }}>
        <Heading style={{
          marginBottom: "16px",
          background: "linear-gradient(135deg, #392735 0%, #D37C67 100%)",
          WebkitBackgroundClip: "text",
          WebkitTextFillColor: "transparent",
          backgroundClip: "text",
          fontWeight: 800,
          letterSpacing: "-0.02em"
        }}>
          Bonjour Migisho! 👋
        </Heading>

        <Text size="5" style={{
          color: "var(--gray-11)",
          marginBottom: "20px",
          fontWeight: 500,
          lineHeight: "1.6"
        }}>
          Bienvenue dans votre espace d&apos;apprentissage personnel

        </Text>

        <Text size="3" style={{
          color: "var(--gray-10)",
          maxWidth: "600px",
          margin: "0 auto",
          lineHeight: "1.7",
          fontWeight: 400
        }}>
          Créez des sessions de tutoriels interactives, explorez de nouveaux sujets,
          posez des questions et partagez vos connaissances avec la communauté.
          Votre parcours d&apos;apprentissage commence ici.
        </Text>

      </div>

      {/* Add pulse animation */}
      <style jsx>{`
        @keyframes pulse {
          0% { opacity: 0.4; transform: scale(1); }
          100% { opacity: 0.8; transform: scale(1.2); }
        }
      `}</style>
      <CreatorBox />

      <div style={{ display: "grid", gap: "20px", gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))" }}>
        {/* <Card style={{ padding: "20px" }}>
          <Heading as="h3" size="4" style={{ marginBottom: "10px" }}>
            Dashboard
          </Heading>
          <Text>
            Your main dashboard content goes here. This area should scroll independently
            while the sidebar remains fixed.
          </Text>
        </Card>

        <Card style={{ padding: "20px" }}>
          <Heading as="h3" size="4" style={{ marginBottom: "10px" }}>
            Recent Activity
          </Heading>
          <Text>
            Recent activity and updates will be displayed in this section.
          </Text>
        </Card>

        <Card style={{ padding: "20px" }}>
          <Heading as="h3" size="4" style={{ marginBottom: "10px" }}>
            Quick Actions
          </Heading>
          <Text>
            Quick action buttons and shortcuts for common tasks.
          </Text>
        </Card> */}
      </div>

      {/* Add some content to test scrolling */}
      <div style={{ marginTop: "40px" }}>
        <Heading as="h2" size="6" style={{ marginBottom: "20px" }}>
        Bibliothèque
        </Heading>
        <Text>
        Explorez les ressources d&apos;apprentissage partage par la comunauté <a href="https://elimuverse.com/community" target="_blank">elimuverse</a>.
        </Text>
        {/* {Array.from({ length: 10 }, (_, i) => (
          <Box key={i} style={{ marginBottom: "20px", padding: "15px", border: "1px solid var(--gray-6)", borderRadius: "8px" }}>
            <Text>
              This is test content item {i + 1}. The sidebar should remain fixed while this content scrolls.
              Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
            </Text>
          </Box>
        ))} */}
        <LibraryBox />
      </div>
    </div>
  );
}
