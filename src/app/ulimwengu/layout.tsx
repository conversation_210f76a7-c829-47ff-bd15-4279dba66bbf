"use client";

import Sidebar from "@/components/layout/sidebar";
import React, { useEffect, useState } from "react";

type LayoutProps = {
  children: React.ReactNode;
  metadata?: {
    title: string;
    description: string;
    canonical: string;
  };
};

const defaultMetadata = {
  title: "Mwalimu",
  description: "Elimuverse Mwalimu",
  canonical: "/somo"
};

export default function AuthLayout({
  children,
  metadata = defaultMetadata
}: LayoutProps) {
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  useEffect(() => {
    // Update document title when metadata changes
    document.title = `${metadata.title} - Mwalimu`;
    
    // Simulate initial loading
    const timer = setTimeout(() => {
      setIsInitialLoading(false);
    }, 2000);

    return () => clearTimeout(timer);
  }, [metadata.title]);

  if (isInitialLoading) {
    return <p>loading...</p>;
  }
  return (
    <>
    <Sidebar/>
     <main>
      {children}
    </main>
    {/* <Toaster position="top-center" /> */}
    </>

  );
}

// Export types and defaults for use in child components
export type { LayoutProps };
export { defaultMetadata };
