import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Heading, Text } from "@radix-ui/themes";
import {
  MagnifyingGlassIcon,
  LayersIcon,
  PersonIcon,
  HomeIcon,
  ChatBubbleIcon,
  GlobeIcon,
  BookmarkIcon,
  FileTextIcon,
  VideoIcon,
  CalendarIcon
} from "@radix-ui/react-icons";
import Logo from "./logo"; // Assuming you have a Logo component

// --- Data for the sidebar items ---
const sidebarItems = [
  {
    icon: <HomeIcon />,
    label: "Home",
    subsections: [
      { label: "Dashboard", icon: <MagnifyingGlassIcon /> },
      { label: "Recent Activity", icon: <BookmarkIcon /> },
      { label: "Quick Actions", icon: <FileTextIcon /> }
    ]
  },
  {
    icon: <ChatBubbleIcon />,
    label: "Sessions",
    subsections: [
      { label: "Active Sessions", icon: <VideoIcon /> },
      { label: "Scheduled", icon: <CalendarIcon /> },
      { label: "Past Sessions", icon: <LayersIcon /> },
      { label: "Session Templates", icon: <FileTextIcon /> }
    ]
  },
  {
    icon: <GlobeIcon />,
    label: "Discover",
    subsections: [
      { label: "Browse Content", icon: <MagnifyingGlassIcon /> },
      { label: "Categories", icon: <LayersIcon /> },
      { label: "Featured", icon: <BookmarkIcon /> },
      { label: "Trending", icon: <GlobeIcon /> }
    ]
  },
  {
    icon: <PersonIcon />,
    label: "Account",
    subsections: [
      { label: "Profile", icon: <PersonIcon /> },
      { label: "Settings", icon: <MagnifyingGlassIcon /> },
      { label: "Preferences", icon: <FileTextIcon /> },
      { label: "Help & Support", icon: <ChatBubbleIcon /> }
    ]
  },
];

export default function Sidebar() {
  // State to track the label of the selected main section
  const [selectedLabel, setSelectedLabel] = React.useState<string | null>("Home");

  // Find the full item object for the selected section
  const selectedItem = sidebarItems.find(item => item.label === selectedLabel);

  return (
    
      <div style={{ display: "flex", height: "100vh", position: "sticky", top: 0 }}>
        {/* --- Primary Sidebar (Icons) --- */}
        <aside
          style={{
            position: "relative",
            width: "80px",
            height: "100vh",
            top: 0,
            left: 0,
            background: "var(--color-panel-solid)",
            boxShadow: "2px 0 16px rgba(0,0,0,0.08)",
            zIndex: 1100,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            padding: "0 8px",
            transition: "width 0.3s ease",
          }}
          aria-label="Primary Sidebar"
        >
          {/* Logo */}
          <div style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: "20px 0",
            height: "64px",
            width: '100%',
            borderBottom: "1px solid var(--gray-a3)"
          }}>
            <Logo />
          </div>
          
          {/* Menu Items */}
          <nav style={{ flex: 1, marginTop: 20, width: '100%' }}>
            {sidebarItems.map((item) => (
              <div key={item.label} style={{ marginBottom: "16px" }}>
                <Button
                  variant={selectedLabel === item.label ? "solid" : "ghost"}
                  color={selectedLabel === item.label ? "cyan" : "gray"}
                  style={{
                    width: "100%",
                    height: "64px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "4px",
                    cursor: "pointer",
                    transition: "all 0.2s ease",
                  }}
                  size="3"
                  title={item.label} // Tooltip for accessibility
                  onClick={() => {
                    // Toggle selection: if already selected, unselect; otherwise, select.
                    if (selectedLabel === item.label) {
                      setSelectedLabel(null);
                    } else {
                      setSelectedLabel(item.label);
                    }
                  }}
                >
                  <div style={{ fontSize: "20px" }}>{item.icon}</div>
                  <span style={{ fontSize: "10px", fontWeight: 500, textAlign: "center", lineHeight: "12px" }}>
                    {item.label}
                  </span>
                </Button>
              </div>
            ))}
          </nav>
          
          {/* Bottom Section - Avatar */}
          <div style={{ padding: "16px 0", marginTop: "auto", borderTop: "1px solid var(--gray-a3)", width: '100%', display: 'flex', justifyContent: 'center' }}>
            <Avatar
              fallback="A"
              size="3"
              radius="full"
              src="/profile.jpg" // Replace with your profile image URL
              alt="Account"
              style={{ cursor: 'pointer' }}
            />
          </div>
        </aside>

        {/* --- Secondary Panel (Subsections) --- */}
        {selectedItem && selectedItem.subsections && (
          <aside
            style={{
              width: "240px",
              height: "100vh",
              background: "var(--color-panel-translucent)", // A slightly different background
              boxShadow: "4px 0 16px rgba(0,0,0,0.05)",
              zIndex: 1000,
              display: "flex",
              flexDirection: "column",
              padding: "0 16px",
              borderRight: "1px solid var(--gray-a3)"
            }}
            aria-label="Subsections Panel"
          >
            {/* Header of the subsection panel */}
            <div style={{ height: '64px', display: 'flex', alignItems: 'center', padding: '20px 0', borderBottom: '1px solid var(--gray-a3)' }}>
                <Heading as="h2" size="4" weight="bold">{selectedItem.label}</Heading>
            </div>
            
            {/* List of subsections */}
            <nav style={{ flex: 1, marginTop: 20 }}>
              {selectedItem.subsections.map((subsection) => (
                <Button
                  key={subsection.label}
                  variant="ghost"
                  color="gray"
                  style={{
                    width: "100%",
                    height: "40px",
                    justifyContent: "flex-start",
                    fontWeight: 500,
                    fontSize: "14px",
                    marginBottom: "4px",
                    padding: "8px 12px",
                    display: "flex",
                    alignItems: "center",
                    gap: "12px"
                  }}
                  size="2"
                >
                  <div style={{ fontSize: "16px" }}>{subsection.icon}</div>
                  <Text as="span" style={{ color: 'var(--gray-12)' }}>{subsection.label}</Text>
                </Button>
              ))}
            </nav>
          </aside>
        )}
      </div>
  );
}