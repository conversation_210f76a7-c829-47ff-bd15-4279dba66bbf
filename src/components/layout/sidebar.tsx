import React from "react";
import { Theme, <PERSON><PERSON>, Avatar, IconButton } from "@radix-ui/themes";
import { HamburgerMenuIcon, Cross1Icon, MagnifyingGlassIcon, LayersIcon, PersonIcon } from "@radix-ui/react-icons";
import Logo from "./logo";

const sidebarItems = [
  { icon: <MagnifyingGlassIcon />, label: "Home", active: true },
  { icon: <LayersIcon />, label: "Sessions" },
  { icon: <LayersIcon />, label: "Discover" },
  { icon: <PersonIcon />, label: "Account" },
];

export default function Sidebar() {
  const [expanded, setExpanded] = React.useState(false);

  return (
    <Theme>
      {/* Sidebar */}
      <aside
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          height: "100vh",
          background: "var(--color-panel-solid)",
          boxShadow: "2px 0 16px rgba(0,0,0,0.08)",
          zIndex: 1100,
          transition: "width 0.3s ease",
          display: "flex",
          flexDirection: "column",
          padding: 0,
          width: expanded ? "220px" : "64px",
          overflow: "hidden",
        }}
        aria-label="Sidebar"
        onMouseEnter={() => setExpanded(true)}
        onMouseLeave={() => setExpanded(false)}
      >
        {/* Logo */}
        <div style={{ 
          display: "flex", 
          alignItems: "center", 
          justifyContent: expanded ? "flex-start" : "center", 
          padding: "20px 16px",
          height: "64px"
        }}>
          <Logo />
          {expanded && <span style={{ marginLeft: 12, fontWeight: 600 }}>Mwalimu</span>}
        </div>
        
        {/* Menu Items */}
        <nav style={{ flex: 1, marginTop: 20 }}>
          {sidebarItems.map((item) => (
            <Button
              key={item.label}
              variant={item.active ? "solid" : "ghost"}
              color={item.active ? "cyan" : "gray"}
              style={{
                width: expanded ? "90%" : "80%",
                margin: "8px auto",
                justifyContent: expanded ? "flex-start" : "center",
                fontWeight: item.active ? 600 : 400,
                padding: expanded ? undefined : "0 8px",
                display: "flex",
              }}
              size="3"
              tabIndex={0}
            >
              <div style={{ 
                display: "flex", 
                alignItems: "center", 
                justifyContent: expanded ? "flex-start" : "center",
                width: "100%" 
              }}>
                {item.icon}
                {expanded && <span style={{ marginLeft: 12 }}>{item.label}</span>}
              </div>
            </Button>
          ))}
        </nav>
        
        {/* Bottom Section - Only visible when expanded */}
        {expanded && (
          <div style={{ margin: 16, marginTop: "auto" }}>
            <div style={{ display: "flex", alignItems: "center", gap: 12, marginBottom: 16 }}>
              <Avatar
                fallback="A"
                size="3"
                radius="full"
                src="/profile.jpg"
                alt="Account"
              />
              <span style={{ color: "var(--gray-12)", fontWeight: 500 }}>Account</span>
            </div>
          </div>
        )}
      </aside>
    </Theme>
  );
}
