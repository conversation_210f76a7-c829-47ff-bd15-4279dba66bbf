import React from "react";
import { <PERSON><PERSON>, <PERSON>tar, Heading, Text } from "@radix-ui/themes";
import {
  MagnifyingGlassIcon,
  LayersIcon,
  PersonIcon,
  HomeIcon,
  ChatBubbleIcon,
  GlobeIcon,
  BookmarkIcon,
  FileTextIcon,
  VideoIcon,
  CalendarIcon
} from "@radix-ui/react-icons";
import Logo from "./logo"; // Assuming you have a Logo component

// --- Data for the sidebar items ---
const sidebarItems = [
  {
    icon: <HomeIcon />,
    label: "Home",
    // subsections: [
    //   { label: "Dashboard", icon: <MagnifyingGlassIcon /> },
    //   { label: "Recent Activity", icon: <BookmarkIcon /> },
    //   { label: "Quick Actions", icon: <FileTextIcon /> }
    // ]
  },
  {
    icon: <ChatBubbleIcon />,
    label: "Sessions",
    subsections: [
      { label: "Active Sessions", icon: <VideoIcon /> },
      { label: "Scheduled", icon: <CalendarIcon /> },
      { label: "Past Sessions", icon: <LayersIcon /> },
      { label: "Session Templates", icon: <FileTextIcon /> }
    ]
  },
  {
    icon: <GlobeIcon />,
    label: "Discover",
    subsections: [
      { label: "Browse Content", icon: <MagnifyingGlassIcon /> },
      { label: "Categories", icon: <LayersIcon /> },
      { label: "Featured", icon: <BookmarkIcon /> },
      { label: "Trending", icon: <GlobeIcon /> }
    ]
  },
  {
    icon: <PersonIcon />,
    label: "Settings",
    subsections: [
      { label: "Profile", icon: <PersonIcon /> },
      { label: "Settings", icon: <MagnifyingGlassIcon /> },
      { label: "Preferences", icon: <FileTextIcon /> },
      { label: "Help & Support", icon: <ChatBubbleIcon /> }
    ]
  },
];

export default function Sidebar() {
  // State to track the label of the selected main section
  const [selectedLabel, setSelectedLabel] = React.useState<string | null>("Home");

  // Find the full item object for the selected section
  const selectedItem = sidebarItems.find(item => item.label === selectedLabel);

  return (
    
      <div style={{ display: "flex", height: "100vh", position: "sticky", top: 0 }}>
        {/* --- Primary Sidebar (Icons) --- */}
        <aside
          style={{
            position: "relative",
            width: "80px",
            height: "100vh",
            top: 0,
            left: 0,
            background: "linear-gradient(180deg, #392735 0%, #2a1f1d 100%)",
            boxShadow: "2px 0 16px rgba(0,0,0,0.08)",
            zIndex: 1100,
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            padding: "0 8px",
            transition: "width 0.3s ease",
          }}
          aria-label="Primary Sidebar"
        >
          {/* Logo */}
          <div style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            padding: "20px 0",
            height: "64px",
            width: '100%',
            borderBottom: "1px solid rgba(255, 255, 255, 0.1)"
          }}>
            <Logo islight={true} />
          </div>
          
          {/* Menu Items */}
          <nav style={{ flex: 1, marginTop: 20, width: '100%' }}>
            {sidebarItems.map((item) => (
              <div key={item.label} style={{ marginBottom: "16px" }}>
                <Button
                  variant="ghost"
                  style={{
                    width: "100%",
                    height: "64px",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    justifyContent: "center",
                    gap: "4px",
                    cursor: "pointer",
                    transition: "all 0.2s ease",
                    backgroundColor: selectedLabel === item.label ? "#D37C67" : "transparent",
                    color: selectedLabel === item.label ? "#ffffff" : "#ffffff",
                    border: selectedLabel === item.label ? "1px solid #D37C67" : "1px solid transparent",
                    borderRadius: "8px",
                  }}
                  size="3"
                  title={item.label} // Tooltip for accessibility
                  onClick={() => {
                    // Toggle selection: if already selected, unselect; otherwise, select.
                    if (selectedLabel === item.label) {
                      setSelectedLabel(null);
                    } else {
                      setSelectedLabel(item.label);
                    }
                  }}
                  onMouseEnter={(e) => {
                    if (selectedLabel !== item.label) {
                      e.currentTarget.style.backgroundColor = "rgba(211, 124, 103, 0.2)";
                      e.currentTarget.style.borderColor = "rgba(211, 124, 103, 0.3)";
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (selectedLabel !== item.label) {
                      e.currentTarget.style.backgroundColor = "transparent";
                      e.currentTarget.style.borderColor = "transparent";
                    }
                  }}
                >
                  <div style={{ fontSize: "20px" }}>{item.icon}</div>
                  <span style={{ fontSize: "10px", fontWeight: 500, textAlign: "center", lineHeight: "12px" }}>
                    {item.label}
                  </span>
                </Button>
              </div>
            ))}
          </nav>
          
          {/* Bottom Section - Avatar */}
          <div style={{ padding: "16px 0", marginTop: "auto", borderTop: "1px solid rgba(255, 255, 255, 0.1)", width: '100%', display: 'flex', justifyContent: 'center' }}>
            <Avatar
              fallback="A"
              size="3"
              radius="full"
              src="/profile.jpg" // Replace with your profile image URL
              alt="Account"
              style={{ cursor: 'pointer' }}
            />
          </div>
        </aside>

        {/* --- Secondary Panel (Subsections) --- */}
        {selectedItem && selectedItem.subsections && (
          <aside
            style={{
              width: "240px",
              height: "100vh",
            //   background: "linear-gradient(180deg, #2a1f1d 0%, #1f1614 100%)",
              boxShadow: "4px 0 16px rgba(0,0,0,0.05)",
              zIndex: 1000,
              display: "flex",
              flexDirection: "column",
              padding: "0 16px",
              borderRight: "1px solid rgba(211, 124, 103, 0.2)"
            }}
            aria-label="Subsections Panel"
          >
            {/* Header of the subsection panel */}
            <div style={{ height: '64px', display: 'flex', alignItems: 'center', padding: '20px 0', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}>
                <Heading as="h2" size="4" weight="bold" style={{ color: "#000" }}>{selectedItem.label}</Heading>
            </div>
            
            {/* List of subsections */}
            <nav style={{ flex: 1, marginTop: 20 }}>
              {selectedItem.subsections.map((subsection) => (
                <Button
                  key={subsection.label}
                  variant="ghost"
                  style={{
                    width: "100%",
                    height: "40px",
                    justifyContent: "flex-start",
                    fontWeight: 500,
                    fontSize: "14px",
                    marginBottom: "4px",
                    padding: "8px 12px",
                    display: "flex",
                    alignItems: "center",
                    gap: "12px",
                    backgroundColor: "transparent",
                    color:  "#000",
                    border: "1px solid transparent",
                    borderRadius: "6px",
                    transition: "all 0.2s ease"
                  }}
                  size="2"
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = "rgba(211, 124, 103, 0.2)";
                    e.currentTarget.style.borderColor = "rgba(211, 124, 103, 0.3)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "transparent";
                    e.currentTarget.style.borderColor = "transparent";
                  }}
                >
                  <div style={{ fontSize: "16px", color: "#D37C67" }}>{subsection.icon}</div>
                  <Text as="span" style={{ color:  "#000", }}>{subsection.label}</Text>
                </Button>
              ))}
            </nav>
          </aside>
        )}
      </div>
  );
}