import React, { useState } from "react";
import { <PERSON>, Button } from "@radix-ui/themes";
import {
  Pencil1Icon,
  UploadIcon,
  ImageIcon,
  Link2Icon,
  ClipboardIcon,
} from "@radix-ui/react-icons";

// --- Helper for action buttons ---
const actionButtons = [
  { label: "Write", icon: <Pencil1Icon /> },
  { label: "Upload file", icon: <UploadIcon /> },
  { label: "Add image", icon: <ImageIcon /> },
  { label: "Add link", icon: <Link2Icon /> },
  { label: "Paste text", icon: <ClipboardIcon /> },
];

// --- Component Props Interface ---
export interface CreatorBoxProps {
  style?: React.CSSProperties;
  onStartSession?: (content: string) => void;
  placeholder?: string;
}

// --- The CreatorBox Component ---
export const CreatorBox = (props: CreatorBoxProps) => {
  const {
    style,
    onStartSession,
    placeholder = "Start a new explainer...",
  } = props;

  // --- State Management ---
  // To store the text from the textarea
  const [textValue, setTextValue] = useState("");
  // To change border color on focus for better UX
  const [isFocused, setIsFocused] = useState(false);

  // --- Derived State ---
  // Check if there is any content to enable the button
  const hasContent = textValue.trim().length > 0;

  // --- Event Handlers ---
  const handleStartSession = () => {
    if (hasContent && onStartSession) {
      onStartSession(textValue);
      // Optional: clear text after starting a session
      // setTextValue(""); 
    }
  };

  // --- Component Styles ---
  // Using CSS-in-JS for dynamic and scoped styling
  const containerStyle: React.CSSProperties = {
    background: "var(--gray-a2)",
    borderRadius: "12px",
    border: `2px solid ${isFocused ? "var(--cyan-9)" : "var(--gray-a5)"}`,
    padding: "16px",
    marginBottom: "20px",
    boxShadow: "0 4px 12px rgba(0,0,0,0.05)",
    transition: "border-color 0.2s ease, box-shadow 0.2s ease",
    width: '100%',
    // maxWidth: '600px', // Set a max-width for better readability
    ...style, // Allow overriding styles via props
  };

  const textareaStyle: React.CSSProperties = {
    width: "100%",
    minHeight: "40px",
    background: "transparent",
    border: "none",
    outline: "none",
    resize: "none", // Or 'vertical' to allow manual resize
    fontSize: "18px",
    fontWeight: 500,
    color: "var(--gray-12)",
    marginBottom: "16px",
  };

  const actionsContainerStyle: React.CSSProperties = {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    flexWrap: 'wrap', // Allows buttons to wrap on smaller screens
    gap: '16px',
  };

  const actionButtonStyle: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    background: "none",
    border: "none",
    cursor: "pointer",
    color: "var(--gray-11)",
    fontSize: "14px",
    fontWeight: 500,
    padding: "4px 8px",
    borderRadius: "6px",
    transition: 'background-color 0.2s ease',
  };

  return (
    <Box style={containerStyle}>
      <textarea
        style={textareaStyle}
        placeholder={placeholder}
        value={textValue}
        onChange={(e) => setTextValue(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        rows={1} // Start with a single row
      />

      <div style={actionsContainerStyle}>
        {/* Left side: Action Buttons */}
        <div style={{ display: "flex", alignItems: "center", gap: "8px", flexWrap: 'wrap' }}>
          {actionButtons.map((action) => (
            <button
              key={action.label}
              title={action.label}
              style={actionButtonStyle}
              // Add a simple hover effect inline
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--gray-a4)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
            >
              {action.icon}
              <span>{action.label}</span>
            </button>
          ))}
        </div>

        {/* Right side: Conditional "Start Session" Button */}
        {hasContent && (
           <Button
            size="3"
            variant="solid"
            color="cyan"
            style={{ fontWeight: 'bold', cursor: 'pointer' }}
            onClick={handleStartSession}
          >
            Start Session
          </Button>
        )}
      </div>
    </Box>
  );
};