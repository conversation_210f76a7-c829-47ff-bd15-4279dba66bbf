import React, { useState } from "react";
import { Box } from "@radix-ui/themes";
import {
  Pencil1Icon,
  UploadIcon,
  ImageIcon,
  Link2Icon,
  ClipboardIcon,
  ArrowRightIcon,
  CodeIcon,
  Cross2Icon,
} from "@radix-ui/react-icons";

// --- Helper for action buttons ---
const actionButtons = [
  { id: "write", label: "É<PERSON>rire", icon: <Pencil1Icon />, color: "#D37C67" },
  { id: "upload", label: "Télécharger", icon: <UploadIcon />, color: "#D37C67" },
  { id: "image", label: "Image", icon: <ImageIcon />, color: "#D37C67" },
  { id: "link", label: "Lien", icon: <Link2Icon />, color: "#D37C67" },
  { id: "paste", label: "Coller", icon: <ClipboardIcon />, color: "#D37C67" },
];

// --- Component Props Interface ---
export interface CreatorBoxProps {
  style?: React.CSSProperties;
  onStartSession?: (content: string) => void;
  placeholder?: string;
}

// --- The CreatorBox Component ---
export const CreatorBox = (props: CreatorBoxProps) => {
  const {
    style,
    onStartSession,
    placeholder = "Commencer un nouvel explicateur...",
  } = props;

  // --- State Management ---
  // To store the text from the textarea
  const [textValue, setTextValue] = useState("");
  // To change border color on focus for better UX
  const [isFocused, setIsFocused] = useState(false);
  // To track which action is currently active
  const [activeAction, setActiveAction] = useState("write");
  // To show/hide upload popup
  const [showUploadPopup, setShowUploadPopup] = useState(false);

  // --- Derived State ---
  // Check if there is any content to enable the button
  const hasContent = textValue.trim().length > 0;

  // --- Event Handlers ---
  const handleStartSession = () => {
    if (hasContent && onStartSession) {
      onStartSession(textValue);
      // Optional: clear text after starting a session
      // setTextValue("");
    }
  };

  const handleActionClick = (actionId: string) => {
    setActiveAction(actionId);
    if (actionId === "upload") {
      setShowUploadPopup(true);
    }
  };

  const handleUploadOption = (option: string) => {
    console.log(`Option sélectionnée: ${option}`);
    setShowUploadPopup(false);
    // Here you would implement the actual upload logic
  };

  // --- Component Styles ---
  // Beautiful glassy design with gradient and glassmorphism
  const containerStyle: React.CSSProperties = {
    background: isFocused
      ? "linear-gradient(135deg, rgba(57, 39, 53, 0.95) 0%, rgba(211, 124, 103, 0.15) 100%)"
      : "linear-gradient(135deg, rgba(57, 39, 53, 0.85) 0%, rgba(57, 39, 53, 0.65) 100%)",
    backdropFilter: "blur(20px)",
    WebkitBackdropFilter: "blur(20px)",
    borderRadius: "24px",
    border: isFocused
      ? "1px solid rgba(211, 124, 103, 0.4)"
      : "1px solid rgba(255, 255, 255, 0.1)",
    padding: "32px",
    marginBottom: "20px",
    boxShadow: isFocused
      ? "0 20px 40px rgba(211, 124, 103, 0.2), 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)"
      : "0 16px 32px rgba(0, 0, 0, 0.2), 0 8px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.05)",
    transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
    width: '100%',
    position: 'relative',
    overflow: 'hidden',
    ...style,
  };

  const textareaStyle: React.CSSProperties = {
    width: "100%",
    minHeight: "60px",
    background: "rgba(255, 255, 255, 0.05)",
    backdropFilter: "blur(10px)",
    WebkitBackdropFilter: "blur(10px)",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    borderRadius: "16px",
    outline: "none",
    resize: "none",
    fontSize: "18px",
    fontWeight: 500,
    color: "#ffffff",
    marginBottom: "24px",
    padding: "16px 20px",
    transition: "all 0.3s ease",
    fontFamily: "inherit",
    lineHeight: "1.5",
  };

  const actionsContainerStyle: React.CSSProperties = {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    flexWrap: 'wrap',
    gap: '20px',
    marginTop: "8px",
  };

  const actionButtonStyle: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    gap: "10px",
    background: "rgba(255, 255, 255, 0.08)",
    backdropFilter: "blur(10px)",
    WebkitBackdropFilter: "blur(10px)",
    border: "1px solid rgba(255, 255, 255, 0.1)",
    borderRadius: "12px",
    cursor: "pointer",
    color: "#ffffff",
    fontSize: "14px",
    fontWeight: 500,
    padding: "12px 16px",
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    position: 'relative',
    overflow: 'hidden',
  };

  return (
    <>
      {/* Custom CSS for placeholder styling and animations */}
      <style jsx>{`
        textarea::placeholder {
          color: rgba(255, 255, 255, 0.5);
          font-style: italic;
          font-weight: 400;
        }
        textarea:focus::placeholder {
          color: rgba(211, 124, 103, 0.7);
        }
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: scale(0.9);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>

      <Box style={containerStyle}>
        {/* Decorative gradient overlay */}
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '2px',
          background: 'linear-gradient(90deg, transparent 0%, #D37C67 50%, transparent 100%)',
          opacity: isFocused ? 1 : 0.3,
          transition: 'opacity 0.3s ease',
        }} />

      <textarea
        style={{
          ...textareaStyle,
          background: isFocused
            ? "rgba(255, 255, 255, 0.1)"
            : "rgba(255, 255, 255, 0.05)",
          borderColor: isFocused
            ? "rgba(211, 124, 103, 0.3)"
            : "rgba(255, 255, 255, 0.1)",
          boxShadow: isFocused
            ? "0 8px 32px rgba(211, 124, 103, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.1)"
            : "0 4px 16px rgba(0, 0, 0, 0.1)",
          transform: isFocused ? 'scale(1.01)' : 'scale(1)',
        }}
        placeholder={placeholder}
        value={textValue}
        onChange={(e) => setTextValue(e.target.value)}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        rows={3}
      />

      <div style={actionsContainerStyle}>
        {/* Left side: Action Buttons */}
        <div style={{ display: "flex", alignItems: "center", gap: "12px", flexWrap: 'wrap' }}>
          {actionButtons.map((action) => {
            const isActive = activeAction === action.id;
            return (
              <button
                key={action.id}
                title={action.label}
                style={{
                  ...actionButtonStyle,
                  background: isActive
                    ? 'rgba(211, 124, 103, 0.3)'
                    : 'rgba(255, 255, 255, 0.08)',
                  borderColor: isActive
                    ? 'rgba(211, 124, 103, 0.5)'
                    : 'rgba(255, 255, 255, 0.1)',
                  boxShadow: isActive
                    ? '0 4px 20px rgba(211, 124, 103, 0.3)'
                    : 'none',
                }}
                onClick={() => handleActionClick(action.id)}
                onMouseEnter={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.background = 'rgba(211, 124, 103, 0.2)';
                    e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.4)';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(211, 124, 103, 0.2)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!isActive) {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = 'none';
                  }
                }}
              >
                <span style={{ color: action.color, fontSize: '16px' }}>{action.icon}</span>
                <span style={{ fontSize: '13px', fontWeight: 600 }}>{action.label}</span>
              </button>
            );
          })}
        </div>

        {/* Right side: Conditional "Start Session" Button */}
        {hasContent && (
          <button
            style={{
              background: 'linear-gradient(135deg, #D37C67 0%, #E8967A 100%)',
              border: 'none',
              borderRadius: '16px',
              color: '#ffffff',
              fontSize: '16px',
              fontWeight: 700,
              padding: '16px 32px',
              cursor: 'pointer',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              boxShadow: '0 8px 25px rgba(211, 124, 103, 0.3)',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
              position: 'relative',
              overflow: 'hidden',
            }}
            onClick={handleStartSession}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-3px) scale(1.02)';
              e.currentTarget.style.boxShadow = '0 12px 35px rgba(211, 124, 103, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0) scale(1)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(211, 124, 103, 0.3)';
            }}
          >
            <span>Commencer la Session</span>
            <ArrowRightIcon style={{ fontSize: '18px' }} />
          </button>
        )}
      </div>

      {/* Upload Options Popup */}
     
    </Box>
    {showUploadPopup && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(10px)',
          WebkitBackdropFilter: 'blur(10px)',
          zIndex: 1000,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          animation: 'fadeIn 0.3s ease',
        }}>
          <div style={{
            background: 'linear-gradient(135deg, rgba(57, 39, 53, 0.95) 0%, rgba(57, 39, 53, 0.85) 100%)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            borderRadius: '24px',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            padding: '32px',
            minWidth: '400px',
            boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3)',
            position: 'relative',
          }}>
            {/* Close button */}
            <button
              style={{
                position: 'absolute',
                top: '16px',
                right: '16px',
                background: 'rgba(255, 255, 255, 0.1)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                borderRadius: '8px',
                color: '#ffffff',
                cursor: 'pointer',
                padding: '8px',
                transition: 'all 0.2s ease',
              }}
              onClick={() => setShowUploadPopup(false)}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(211, 124, 103, 0.3)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';
              }}
            >
              <Cross2Icon />
            </button>

            <h3 style={{
              color: '#ffffff',
              fontSize: '24px',
              fontWeight: 700,
              marginBottom: '24px',
              textAlign: 'center',
            }}>
              Options de Téléchargement
            </h3>

            <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              {[
                { id: 'file', label: 'Télécharger un fichier', icon: <UploadIcon /> },
                { id: 'google-drive', label: 'Depuis Google Drive', icon: <Link2Icon /> },
                { id: 'import-code', label: 'Importer du code', icon: <CodeIcon /> },
              ].map((option) => (
                <button
                  key={option.id}
                  style={{
                    background: 'rgba(255, 255, 255, 0.08)',
                    backdropFilter: 'blur(10px)',
                    WebkitBackdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255, 255, 255, 0.1)',
                    borderRadius: '16px',
                    color: '#ffffff',
                    fontSize: '16px',
                    fontWeight: 600,
                    padding: '16px 24px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '12px',
                    width: '100%',
                  }}
                  onClick={() => handleUploadOption(option.id)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.background = 'rgba(211, 124, 103, 0.2)';
                    e.currentTarget.style.borderColor = 'rgba(211, 124, 103, 0.4)';
                    e.currentTarget.style.transform = 'translateY(-2px)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
                    e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    e.currentTarget.style.transform = 'translateY(0)';
                  }}
                >
                  <span style={{ color: '#D37C67', fontSize: '20px' }}>{option.icon}</span>
                  <span>{option.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
};