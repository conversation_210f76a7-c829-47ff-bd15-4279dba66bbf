/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useMemo } from "react";
import { Box, Flex, Text, Heading, Card } from "@radix-ui/themes";
import { DotsHorizontalIcon } from "@radix-ui/react-icons";

// --- Mock Data: Represents items in your library ---
const libraryItems = [
  {
    id: 1,
    category: "Lecture Sessions",
    title: "The Fundamentals of Quantum Physics",
    description: "Created: June 8, 2025 | 5 Sources",
    icon: "⚛️",
  },
  {
    id: 2,
    category: "Lecture Sessions",
    title: "Introduction to Web Development",
    description: "Created: June 8, 2025 | 5 Sources",
    icon: "💻",
  },
  {
    id: 3,
    category: "Books",
    title: "The Rise and Fall of Ancient Rome",
    description: "By Mary Beard | 5 Sources",
    icon: "🏛️",
  },
  {
    id: 4,
    category: "Papers",
    title: "The Process of Photosynthesis",
    description: "Published in Nature | 50 Sources",
    icon: "🌻",
  },
  {
    id: 5,
    category: "Lecture Sessions",
    title: "Brainstorm Buddy",
    description: "Explore and expand your ideas with a creative AI.",
    icon: "💡",
  },
  {
    id: 6,
    category: "Books",
    title: "Money Manager",
    description: "Take control of your finances—track spending & plan budgets.",
    icon: "💳",
  },
  {
    id: 7,
    category: "Tips",
    title: "Writing Coach",
    description: "Get clear, actionable feedback on your writing.",
    icon: "✍️",
  },
  {
    id: 8,
    category: "Past Papers",
    title: "YC Startup Advisor",
    description: "Get guidance on your startup journey.",
    icon: "🚀",
  },
];

const TABS = ["All", "Lecture Sessions", "Books", "Papers", "Tips", "Past Papers"];

// --- The Main Library Page Component ---
export const LibraryBox  = () => {
  const [activeTab, setActiveTab] = useState(TABS[0]);

  // Filter items based on the active tab
  const filteredItems = useMemo(() => {
    if (activeTab === "All") {
      return libraryItems;
    }
    return libraryItems.filter((item) => item.category === activeTab);
  }, [activeTab]);

  return (
    <Box style={{ padding: "24px 48px", background: "var(--gray-1)", minHeight: '100vh' }}>
      {/* Tab Navigation */}
      <Flex as="nav" gap="24px" style={{ borderBottom: "1px solid var(--gray-a5)", marginBottom: "32px" }}>
        {TABS.map((tab) => (
          <TabButton
            key={tab}
            label={tab}
            isActive={activeTab === tab}
            onClick={() => setActiveTab(tab)}
          />
        ))}
      </Flex>

      {/* Item Grid */}
      <Box 
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fill, minmax(280px, 1fr))",
          gap: "24px",
        }}
      >
        {filteredItems.map((item) => (
          <LibraryItemCard key={item.id} item={item} />
        ))}
      </Box>
    </Box>
  );
};


// --- Sub-component for a single Tab Button ---
const TabButton = ({ label, isActive, onClick }) => {
  const style: React.CSSProperties = {
    padding: "8px 4px 12px 4px",
    cursor: "pointer",
    background: "none",
    border: "none",
    borderBottom: `2px solid ${isActive ? "var(--cyan-9)" : "transparent"}`,
    color: isActive ? "var(--cyan-9)" : "var(--gray-11)",
    fontWeight: isActive ? 600 : 500,
    fontSize: "16px",
    transition: "all 0.2s ease-in-out",
  };
  return <button style={style} onClick={onClick}>{label}</button>;
};


// --- Sub-component for a single Library Item Card ---
const LibraryItemCard = ({ item }) => {
    // We'll manage hover state to apply the shadow
    const [isHovered, setIsHovered] = useState(false);

    const cardStyle: React.CSSProperties = {
        background: "var(--gray-a2)",
        borderRadius: "12px",
        padding: "20px",
        border: '1px solid var(--gray-a5)',
        transition: 'transform 0.2s ease, box-shadow 0.2s ease',
        transform: isHovered ? 'translateY(-4px)' : 'translateY(0)',
        boxShadow: isHovered ? '0 8px 24px rgba(0,0,0,0.08)' : 'none',
        cursor: 'pointer',
    };

  return (
    <div 
        style={cardStyle} 
        onMouseEnter={() => setIsHovered(true)} 
        onMouseLeave={() => setIsHovered(false)}
    >
      <Flex direction="column" height="100%">
        {/* Top part: Icon */}
        <Box style={{ fontSize: "24px", marginBottom: "16px" }}>{item.icon}</Box>

        {/* Middle part: Title (takes up available space) */}
        <Box style={{ flexGrow: 1, marginBottom: "16px" }}>
          <Heading as="h3" size="4" style={{ color: "var(--gray-12)", marginBottom: '8px' }}>
            {item.title}
          </Heading>
           <Text as="p" size="2" style={{ color: "var(--gray-11)", lineHeight: 1.5 }}>
             {item.description}
           </Text>
        </Box>

        {/* Bottom part: Options Icon */}
        <Flex justify="end">
          <DotsHorizontalIcon color="var(--gray-9)" width="20" height="20" />
        </Flex>
      </Flex>
    </div>
  );
};