import React, { useState, useMemo } from "react";
import { Box, Flex, Text, Heading } from "@radix-ui/themes";
import {
  DotsHorizontalIcon,
  VideoIcon,
  ReaderIcon,
  FileTextIcon,
  LightningBoltIcon,
  FileIcon,
  StarIcon
} from "@radix-ui/react-icons";

// --- Categories with French names and icons ---
const CATEGORIES = {
  "Sessions de Cours": { icon: <VideoIcon />, color: "#D37C67" },
  "Livres": { icon: <ReaderIcon />, color: "#D37C67" },
  "Articles": { icon: <FileTextIcon />, color: "#D37C67" },
  "Conseils": { icon: <LightningBoltIcon />, color: "#D37C67" },
  "Examens Passés": { icon: <FileIcon />, color: "#D37C67" },
};

// --- Mock Data: Represents items in your library ---
const libraryItems = [
  {
    id: 1,
    category: "Sessions de Cours",
    title: "Les Fondamentaux de la Physique Quantique",
    description: "Créé: 8 Juin 2025 | 5 Sources",
    icon: "⚛️",
  },
  {
    id: 2,
    category: "Sessions de Cours",
    title: "Introduction au Développement Web",
    description: "Créé: 8 Juin 2025 | 5 Sources",
    icon: "💻",
  },
  {
    id: 3,
    category: "Livres",
    title: "L'Essor et la Chute de la Rome Antique",
    description: "Par Mary Beard | 5 Sources",
    icon: "📚",
  },
  {
    id: 4,
    category: "Articles",
    title: "Le Processus de la Photosynthèse",
    description: "Publié dans Nature | 50 Sources",
    icon: "🌻",
  },
  {
    id: 5,
    category: "Sessions de Cours",
    title: "Assistant de Brainstorming",
    description: "Explorez et développez vos idées avec une IA créative.",
    icon: "💡",
  },
  {
    id: 6,
    category: "Livres",
    title: "Gestionnaire de Budget",
    description: "Prenez le contrôle de vos finances—suivez les dépenses et planifiez.",
    icon: "💳",
  },
  {
    id: 7,
    category: "Conseils",
    title: "Coach d'Écriture",
    description: "Obtenez des commentaires clairs et pratiques sur votre écriture.",
    icon: "✍️",
  },
  {
    id: 8,
    category: "Examens Passés",
    title: "Conseiller de Startup YC",
    description: "Obtenez des conseils pour votre parcours entrepreneurial.",
    icon: "🚀",
  },
];

const TABS = ["Tout", ...Object.keys(CATEGORIES)];

// --- The Main Library Page Component ---
export const LibraryBox = () => {
  const [activeTab, setActiveTab] = useState(TABS[0]);

  // Filter items based on the active tab
  const filteredItems = useMemo(() => {
    if (activeTab === "Tout") {
      return libraryItems;
    }
    return libraryItems.filter((item) => item.category === activeTab);
  }, [activeTab]);

  return (
    <Box style={{
      padding: "32px",
      background: "linear-gradient(135deg, rgba(57, 39, 53, 0.02) 0%, rgba(211, 124, 103, 0.02) 100%)",
      minHeight: '100vh'
    }}>
      {/* Tab Navigation */}
      <div style={{
        display: "flex",
        gap: "16px",
        marginBottom: "32px",
        flexWrap: "wrap",
        justifyContent: "center"
      }}>
        {TABS.map((tab) => (
          <TabButton
            key={tab}
            label={tab}
            isActive={activeTab === tab}
            onClick={() => setActiveTab(tab)}
          />
        ))}
      </div>

      {/* Item Grid */}
      <Box
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fill, minmax(320px, 1fr))",
          gap: "24px",
        }}
      >
        {filteredItems.map((item) => (
          <LibraryItemCard key={item.id} item={item} />
        ))}
      </Box>
    </Box>
  );
};


// --- Sub-component for a single Tab Button ---
const TabButton = ({ label, isActive, onClick }: { label: string; isActive: boolean; onClick: () => void }) => {
  const getCategoryIcon = (categoryName: string) => {
    if (categoryName === "Tout") return <StarIcon />;
    return CATEGORIES[categoryName as keyof typeof CATEGORIES]?.icon || null;
  };

  const style: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    padding: "12px 20px",
    cursor: "pointer",
    background: isActive
      ? "linear-gradient(135deg, rgba(211, 124, 103, 0.15) 0%, rgba(57, 39, 53, 0.1) 100%)"
      : "rgba(255, 255, 255, 0.05)",
    backdropFilter: "blur(10px)",
    WebkitBackdropFilter: "blur(10px)",
    border: isActive
      ? "1px solid rgba(211, 124, 103, 0.3)"
      : "1px solid rgba(255, 255, 255, 0.1)",
    borderRadius: "16px",
    color: isActive ? "#D37C67" : "var(--gray-11)",
    fontWeight: isActive ? 600 : 500,
    fontSize: "14px",
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
    boxShadow: isActive
      ? "0 4px 20px rgba(211, 124, 103, 0.2)"
      : "0 2px 8px rgba(0, 0, 0, 0.05)",
  };

  return (
    <button
      style={style}
      onClick={onClick}
      onMouseEnter={(e) => {
        if (!isActive) {
          e.currentTarget.style.background = "rgba(211, 124, 103, 0.1)";
          e.currentTarget.style.borderColor = "rgba(211, 124, 103, 0.2)";
          e.currentTarget.style.transform = "translateY(-2px)";
        }
      }}
      onMouseLeave={(e) => {
        if (!isActive) {
          e.currentTarget.style.background = "rgba(255, 255, 255, 0.05)";
          e.currentTarget.style.borderColor = "rgba(255, 255, 255, 0.1)";
          e.currentTarget.style.transform = "translateY(0)";
        }
      }}
    >
      <span style={{ fontSize: "16px", color: isActive ? "#D37C67" : "var(--gray-10)" }}>
        {getCategoryIcon(label)}
      </span>
      <span>{label}</span>
    </button>
  );
};


// --- Sub-component for a single Library Item Card ---
const LibraryItemCard = ({ item }: { item: { id: number; category: string; title: string; description: string; icon: string } }) => {
    const [isHovered, setIsHovered] = useState(false);

    const cardStyle: React.CSSProperties = {
        background: isHovered
          ? "linear-gradient(135deg, rgba(57, 39, 53, 0.08) 0%, rgba(211, 124, 103, 0.05) 100%)"
          : "linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)",
        backdropFilter: "blur(20px)",
        WebkitBackdropFilter: "blur(20px)",
        borderRadius: "20px",
        padding: "24px",
        border: isHovered
          ? '1px solid rgba(211, 124, 103, 0.2)'
          : '1px solid rgba(255, 255, 255, 0.1)',
        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
        transform: isHovered ? 'translateY(-8px) scale(1.02)' : 'translateY(0) scale(1)',
        boxShadow: isHovered
          ? '0 20px 40px rgba(211, 124, 103, 0.15), 0 8px 32px rgba(0, 0, 0, 0.1)'
          : '0 8px 24px rgba(0, 0, 0, 0.05)',
        cursor: 'pointer',
        position: 'relative',
        overflow: 'hidden',
    };

    const getCategoryInfo = (category: string) => {
      return CATEGORIES[category as keyof typeof CATEGORIES] || { icon: <FileTextIcon />, color: "#D37C67" };
    };

    const categoryInfo = getCategoryInfo(item.category);

  return (
    <div
        style={cardStyle}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
    >
      {/* Decorative gradient overlay */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '3px',
        background: `linear-gradient(90deg, transparent 0%, ${categoryInfo.color} 50%, transparent 100%)`,
        opacity: isHovered ? 1 : 0.3,
        transition: 'opacity 0.3s ease',
      }} />

      <Flex direction="column" height="100%">
        {/* Category and Icon */}
        <Flex align="center" justify="between" style={{ marginBottom: "20px" }}>
          <Flex align="center" gap="8px">
            <span style={{ color: categoryInfo.color, fontSize: "18px" }}>
              {categoryInfo.icon}
            </span>
            <Text size="1" style={{
              color: categoryInfo.color,
              fontWeight: 600,
              textTransform: "uppercase",
              letterSpacing: "0.5px"
            }}>
              {item.category}
            </Text>
          </Flex>
          <div style={{ fontSize: "32px" }}>{item.icon}</div>
        </Flex>

        {/* Title and Description */}
        <Box style={{ flexGrow: 1, marginBottom: "20px" }}>
          <Heading as="h3" size="4" style={{
            color: "var(--gray-12)",
            marginBottom: '12px',
            lineHeight: "1.3",
            fontWeight: 700
          }}>
            {item.title}
          </Heading>
           <Text as="p" size="2" style={{
             color: "var(--gray-11)",
             lineHeight: 1.6,
             fontWeight: 400
           }}>
             {item.description}
           </Text>
        </Box>

        {/* Bottom Actions */}
        <Flex justify="between" align="center">
          <div style={{
            padding: "6px 12px",
            background: "rgba(211, 124, 103, 0.1)",
            borderRadius: "12px",
            border: "1px solid rgba(211, 124, 103, 0.2)"
          }}>
            <Text size="1" style={{ color: "#D37C67", fontWeight: 600 }}>
              Ouvrir
            </Text>
          </div>
          <button style={{
            background: "none",
            border: "none",
            cursor: "pointer",
            padding: "8px",
            borderRadius: "8px",
            transition: "background 0.2s ease"
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = "rgba(211, 124, 103, 0.1)";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = "none";
          }}
          >
            <DotsHorizontalIcon color="var(--gray-9)" width="20" height="20" />
          </button>
        </Flex>
      </Flex>
    </div>
  );
};