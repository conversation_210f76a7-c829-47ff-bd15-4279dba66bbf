import React, { useState, useMemo } from "react";
import { Box, Flex, Text, Heading } from "@radix-ui/themes";
import {
  DotsHorizontalIcon,
  VideoIcon,
  ReaderIcon,
  FileTextIcon,
  FileIcon,
  StarIcon,
  DotFilledIcon,
  PersonIcon,
  PlayIcon
} from "@radix-ui/react-icons";

// --- Categories with French names and icons ---
const CATEGORIES = {
  "Live Sessions": { icon: <DotFilledIcon />, color: "#D37C67", isLive: true },
  "Sessions de Cours": { icon: <VideoIcon />, color: "#D37C67" },
  "Livres": { icon: <ReaderIcon />, color: "#D37C67" },
  "Articles": { icon: <FileTextIcon />, color: "#D37C67" },
  "Examens Passés": { icon: <FileIcon />, color: "#D37C67" },
};

// --- Mock Data: Represents items in your library ---
const libraryItems = [
  // Live Sessions
  {
    id: 101,
    category: "Live Sessions",
    title: "Mathématiques Avancées - Calcul Intégral",
    description: "Session interactive avec Dr. <PERSON>",
    icon: "📐",
    isLive: true,
    liveViewers: 24,
    duration: "45 min",
  },
  {
    id: 102,
    category: "Live Sessions",
    title: "Programmation Python pour Débutants",
    description: "Apprenez les bases avec des exemples pratiques",
    icon: "🐍",
    isLive: true,
    liveViewers: 67,
    duration: "1h 20min",
  },
  {
    id: 103,
    category: "Live Sessions",
    title: "Histoire de l'Art Renaissance",
    description: "Exploration des chefs-d'œuvre de la Renaissance",
    icon: "🎨",
    isLive: true,
    liveViewers: 15,
    duration: "30 min",
  },
  // Regular Sessions
  {
    id: 1,
    category: "Sessions de Cours",
    title: "Les Fondamentaux de la Physique Quantique",
    description: "Créé: 8 Juin 2025 | 5 Sources",
    icon: "⚛️",
  },
  {
    id: 2,
    category: "Sessions de Cours",
    title: "Introduction au Développement Web",
    description: "Créé: 8 Juin 2025 | 5 Sources",
    icon: "💻",
  },
  {
    id: 3,
    category: "Livres",
    title: "L'Essor et la Chute de la Rome Antique",
    description: "Par Mary Beard | 5 Sources",
    icon: "📚",
  },
  {
    id: 4,
    category: "Articles",
    title: "Le Processus de la Photosynthèse",
    description: "Publié dans Nature | 50 Sources",
    icon: "🌻",
  },
  {
    id: 5,
    category: "Sessions de Cours",
    title: "Assistant de Brainstorming",
    description: "Explorez et développez vos idées avec une IA créative.",
    icon: "💡",
  },
  {
    id: 6,
    category: "Livres",
    title: "Gestionnaire de Budget",
    description: "Prenez le contrôle de vos finances—suivez les dépenses et planifiez.",
    icon: "💳",
  },
  {
    id: 8,
    category: "Examens Passés",
    title: "Conseiller de Startup YC",
    description: "Obtenez des conseils pour votre parcours entrepreneurial.",
    icon: "🚀",
  },
];

const TABS = ["Tout", ...Object.keys(CATEGORIES)];

// Helper function to count live sessions
const getLiveSessionCount = () => {
  return libraryItems.filter(item => item.isLive).length;
};

// --- The Main Library Page Component ---
export const LibraryBox = () => {
  const [activeTab, setActiveTab] = useState(TABS[0]);
  const liveSessionCount = getLiveSessionCount();

  // Filter items based on the active tab
  const filteredItems = useMemo(() => {
    if (activeTab === "Tout") {
      // Exclude live sessions from "Tout" - only show regular library items
      return libraryItems.filter((item) => !item.isLive);
    }
    return libraryItems.filter((item) => item.category === activeTab);
  }, [activeTab]);

  return (
    <Box style={{
      marginTop: "10px",
      padding: "32px",
      background: "linear-gradient(135deg, rgba(57, 39, 53, 0.02) 0%, rgba(211, 124, 103, 0.02) 100%)",
      minHeight: '100vh',
      borderRadius: "14px"
    }}>
      {/* Tab Navigation */}
      <div style={{
        display: "flex",
        gap: "16px",
        marginBottom: "32px",
        flexWrap: "wrap",
        justifyContent: "center"
      }}>
        {TABS.map((tab) => (
          <TabButton
            key={tab}
            label={tab}
            isActive={activeTab === tab}
            onClick={() => setActiveTab(tab)}
            liveCount={tab === "Live Sessions" ? liveSessionCount : undefined}
          />
        ))}
      </div>

      {/* Item Grid */}
      <Box
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fill, minmax(320px, 1fr))",
          gap: "24px",
        }}
      >
        {filteredItems.map((item) => (
          item.isLive ? (
            <LiveSessionCard key={item.id} item={item} />
          ) : (
            <LibraryItemCard key={item.id} item={item} />
          )
        ))}
      </Box>
    </Box>
  );
};


// --- Sub-component for a single Tab Button ---
const TabButton = ({
  label,
  isActive,
  onClick,
  liveCount
}: {
  label: string;
  isActive: boolean;
  onClick: () => void;
  liveCount?: number;
}) => {
  const getCategoryIcon = (categoryName: string) => {
    if (categoryName === "Tout") return <StarIcon />;
    return CATEGORIES[categoryName as keyof typeof CATEGORIES]?.icon || null;
  };

  const isLiveTab = label === "Live Sessions";
  const hasLiveSessions = liveCount && liveCount > 0;

  const style: React.CSSProperties = {
    display: "flex",
    alignItems: "center",
    gap: "8px",
    padding: "12px 20px",
    cursor: "pointer",
    background: isActive
      ? "linear-gradient(135deg, rgba(211, 124, 103, 0.15) 0%, rgba(57, 39, 53, 0.1) 100%)"
      : "rgba(255, 255, 255, 0.05)",
    backdropFilter: "blur(10px)",
    WebkitBackdropFilter: "blur(10px)",
    border: isActive
      ? "1px solid rgba(211, 124, 103, 0.3)"
      : "1px solid rgba(255, 255, 255, 0.1)",
    borderRadius: "16px",
    color: isActive ? "#D37C67" : "var(--gray-11)",
    fontWeight: isActive ? 600 : 500,
    fontSize: "14px",
    transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
    boxShadow: isActive
      ? "0 4px 20px rgba(211, 124, 103, 0.2)"
      : "0 2px 8px rgba(0, 0, 0, 0.05)",
    position: "relative",
    overflow: "hidden",
  };

  return (
    <>
      {/* CSS for live animation */}
      {isLiveTab && hasLiveSessions && (
        <style jsx>{`
          @keyframes liveGlow {
            0%, 100% {
              box-shadow: 0 0 5px rgba(211, 124, 103, 0.5), 0 0 10px rgba(211, 124, 103, 0.3), 0 0 15px rgba(211, 124, 103, 0.1);
              border-color: rgba(211, 124, 103, 0.6);
            }
            50% {
              box-shadow: 0 0 10px rgba(211, 124, 103, 0.8), 0 0 20px rgba(211, 124, 103, 0.5), 0 0 30px rgba(211, 124, 103, 0.3);
              border-color: rgba(211, 124, 103, 0.9);
            }
          }
          .live-tab {
            animation: liveGlow 2s ease-in-out infinite;
          }
        `}</style>
      )}

      <button
        className={isLiveTab && hasLiveSessions ? "live-tab" : ""}
        style={style}
        onClick={onClick}
        onMouseEnter={(e) => {
          if (!isActive) {
            e.currentTarget.style.background = "rgba(211, 124, 103, 0.1)";
            e.currentTarget.style.borderColor = "rgba(211, 124, 103, 0.2)";
            e.currentTarget.style.transform = "translateY(-2px)";
          }
        }}
        onMouseLeave={(e) => {
          if (!isActive) {
            e.currentTarget.style.background = "rgba(255, 255, 255, 0.05)";
            e.currentTarget.style.borderColor = "rgba(255, 255, 255, 0.1)";
            e.currentTarget.style.transform = "translateY(0)";
          }
        }}
      >
        <span style={{ fontSize: "16px", color: isActive ? "#D37C67" : "var(--gray-10)" }}>
          {getCategoryIcon(label)}
        </span>
        <span>{label}</span>
        {isLiveTab && hasLiveSessions && (
          <span style={{
            background: "linear-gradient(135deg, #D37C67 0%, #E8967A 100%)",
            color: "#ffffff",
            fontSize: "11px",
            fontWeight: 700,
            padding: "2px 6px",
            borderRadius: "8px",
            marginLeft: "4px",
            minWidth: "20px",
            textAlign: "center",
            boxShadow: "0 2px 8px rgba(211, 124, 103, 0.3)"
          }}>
            {liveCount}
          </span>
        )}
      </button>
    </>
  );
};


// --- Sub-component for a single Library Item Card ---
const LibraryItemCard = ({ item }: { item: { id: number; category: string; title: string; description: string; icon: string } }) => {
    const [isHovered, setIsHovered] = useState(false);

    const cardStyle: React.CSSProperties = {
        background: isHovered
          ? "linear-gradient(135deg, rgba(57, 39, 53, 0.08) 0%, rgba(211, 124, 103, 0.05) 100%)"
          : "linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)",
        backdropFilter: "blur(20px)",
        WebkitBackdropFilter: "blur(20px)",
        borderRadius: "20px",
        padding: "24px",
        border: isHovered
          ? '1px solid rgba(211, 124, 103, 0.2)'
          : '1px solid rgba(255, 255, 255, 0.1)',
        transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
        transform: isHovered ? 'translateY(-8px) scale(1.02)' : 'translateY(0) scale(1)',
        boxShadow: isHovered
          ? '0 20px 40px rgba(211, 124, 103, 0.15), 0 8px 32px rgba(0, 0, 0, 0.1)'
          : '0 8px 24px rgba(0, 0, 0, 0.05)',
        cursor: 'pointer',
        position: 'relative',
        overflow: 'hidden',
    };

    const getCategoryInfo = (category: string) => {
      return CATEGORIES[category as keyof typeof CATEGORIES] || { icon: <FileTextIcon />, color: "#D37C67" };
    };

    const categoryInfo = getCategoryInfo(item.category);

  return (
    <div
        style={cardStyle}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
    >
      {/* Decorative gradient overlay */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '3px',
        background: `linear-gradient(90deg, transparent 0%, ${categoryInfo.color} 50%, transparent 100%)`,
        opacity: isHovered ? 1 : 0.3,
        transition: 'opacity 0.3s ease',
      }} />

      <Flex direction="column" height="100%">
        {/* Category and Icon */}
        <Flex align="center" justify="between" style={{ marginBottom: "20px" }}>
          <Flex align="center" gap="8px">
            <span style={{ color: categoryInfo.color, fontSize: "18px" }}>
              {categoryInfo.icon}
            </span>
            <Text size="1" style={{
              color: categoryInfo.color,
              fontWeight: 600,
              textTransform: "uppercase",
              letterSpacing: "0.5px"
            }}>
              {item.category}
            </Text>
          </Flex>
          <div style={{ fontSize: "32px" }}>{item.icon}</div>
        </Flex>

        {/* Title and Description */}
        <Box style={{ flexGrow: 1, marginBottom: "20px" }}>
          <Heading as="h3" size="4" style={{
            color: "var(--gray-12)",
            marginBottom: '12px',
            lineHeight: "1.3",
            fontWeight: 700
          }}>
            {item.title}
          </Heading>
           <Text as="p" size="2" style={{
             color: "var(--gray-11)",
             lineHeight: 1.6,
             fontWeight: 400
           }}>
             {item.description}
           </Text>
        </Box>

        {/* Bottom Actions */}
        <Flex justify="between" align="center">
          <div style={{
            padding: "6px 12px",
            background: "rgba(211, 124, 103, 0.1)",
            borderRadius: "12px",
            border: "1px solid rgba(211, 124, 103, 0.2)"
          }}>
            <Text size="1" style={{ color: "#D37C67", fontWeight: 600 }}>
              Ouvrir
            </Text>
          </div>
          <button style={{
            background: "none",
            border: "none",
            cursor: "pointer",
            padding: "8px",
            borderRadius: "8px",
            transition: "background 0.2s ease"
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.background = "rgba(211, 124, 103, 0.1)";
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.background = "none";
          }}
          >
            <DotsHorizontalIcon color="var(--gray-9)" width="20" height="20" />
          </button>
        </Flex>
      </Flex>
    </div>
  );
};

// --- Live Session Card Component ---
const LiveSessionCard = ({ item }: {
  item: {
    id: number;
    category: string;
    title: string;
    description: string;
    icon: string;
    isLive: boolean;
    liveViewers: number;
    duration: string;
  }
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const cardStyle: React.CSSProperties = {
    background: isHovered
      ? "linear-gradient(135deg, rgba(211, 124, 103, 0.15) 0%, rgba(57, 39, 53, 0.1) 100%)"
      : "linear-gradient(135deg, rgba(211, 124, 103, 0.08) 0%, rgba(57, 39, 53, 0.05) 100%)",
    backdropFilter: "blur(20px)",
    WebkitBackdropFilter: "blur(20px)",
    borderRadius: "20px",
    padding: "24px",
    border: "2px solid rgba(211, 124, 103, 0.3)",
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    transform: isHovered ? 'translateY(-8px) scale(1.02)' : 'translateY(0) scale(1)',
    boxShadow: isHovered
      ? '0 20px 40px rgba(211, 124, 103, 0.25), 0 8px 32px rgba(0, 0, 0, 0.1)'
      : '0 12px 28px rgba(211, 124, 103, 0.15), 0 4px 16px rgba(0, 0, 0, 0.05)',
    cursor: 'pointer',
    position: 'relative',
    overflow: 'hidden',
  };

  return (
    <>
      {/* CSS for live pulse animation */}
      <style jsx>{`
        @keyframes livePulse {
          0%, 100% {
            opacity: 1;
            transform: scale(1);
          }
          50% {
            opacity: 0.7;
            transform: scale(1.05);
          }
        }
        @keyframes borderGlow {
          0%, 100% {
            border-color: rgba(211, 124, 103, 0.3);
            box-shadow: 0 0 10px rgba(211, 124, 103, 0.2);
          }
          50% {
            border-color: rgba(211, 124, 103, 0.6);
            box-shadow: 0 0 20px rgba(211, 124, 103, 0.4);
          }
        }
        .live-card {
          animation: borderGlow 2s ease-in-out infinite;
        }
        .live-indicator {
          animation: livePulse 1.5s ease-in-out infinite;
        }
      `}</style>

      <div
        className="live-card"
        style={cardStyle}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Live indicator overlay */}
        <div style={{
          position: 'absolute',
          top: '16px',
          right: '16px',
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
          background: 'rgba(211, 124, 103, 0.9)',
          padding: '6px 12px',
          borderRadius: '20px',
          fontSize: '12px',
          fontWeight: 700,
          color: '#ffffff',
          boxShadow: '0 4px 12px rgba(211, 124, 103, 0.3)'
        }}>
          <DotFilledIcon className="live-indicator" style={{ color: '#ffffff' }} />
          <div> <span>LIVE </span>| <PersonIcon style={{ color: '#FFF', marginRight: '4px', width: '12px', height: '12px' }} />
              <Text size="1" style={{ color: '#FFF', fontWeight: 600 }}>
                {item.liveViewers}
              </Text> </div>
        </div>

        <Flex direction="column" height="100%">
          {/* Header with icon and viewers */}
          <Flex align="center" justify="between" style={{ marginBottom: "20px" }}>
            <div style={{ fontSize: "40px" }}>{item.icon}</div>
            {/* <Flex align="center" gap="8px" style={{
              background: 'rgba(255, 255, 255, 0.1)',
              padding: '8px 12px',
              borderRadius: '12px',
              border: '1px solid rgba(255, 255, 255, 0.2)'
            }}>
            </Flex> */}
          </Flex>

          {/* Title and Description */}
          <Box style={{ flexGrow: 1, marginBottom: "20px" }}>
            <Heading as="h3" size="4" style={{
              color: "var(--gray-12)",
              marginBottom: '8px',
              lineHeight: "1.3",
              fontWeight: 700
            }}>
              {item.title}
            </Heading>
            <Text as="p" size="2" style={{
              color: "var(--gray-11)",
              lineHeight: 1.6,
              fontWeight: 400,
              marginBottom: '12px'
            }}>
              {item.description}
            </Text>
            <Text size="1" style={{
              color: '#D37C67',
              fontWeight: 600,
              textTransform: 'uppercase',
              letterSpacing: '0.5px'
            }}>
              Durée: {item.duration}
            </Text>
          </Box>

          {/* Join Session Button */}
          <button
            style={{
              background: 'linear-gradient(135deg, #D37C67 0%, #E8967A 100%)',
              border: 'none',
              borderRadius: '16px',
              color: '#ffffff',
              fontSize: '16px',
              fontWeight: 700,
              padding: '16px 24px',
              cursor: 'pointer',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
              boxShadow: '0 8px 25px rgba(211, 124, 103, 0.3)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              gap: '8px',
              width: '100%',
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)';
              e.currentTarget.style.boxShadow = '0 12px 35px rgba(211, 124, 103, 0.4)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0) scale(1)';
              e.currentTarget.style.boxShadow = '0 8px 25px rgba(211, 124, 103, 0.3)';
            }}
          >
            <PlayIcon style={{ fontSize: '18px' }} />
            <span>Rejoindre la Session</span>
          </button>
        </Flex>
      </div>
    </>
  );
};