{"name": "griot", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-icons": "^1.3.2", "@radix-ui/themes": "^3.2.1", "@types/js-cookie": "^3.0.6", "convex": "^1.24.8", "dotenv": "^16.5.0", "js-cookie": "^3.0.5", "next": "15.3.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "typescript": "^5"}}